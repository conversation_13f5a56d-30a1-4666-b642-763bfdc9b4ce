/**
 * Tab Manager Service - Manages multiple web application tabs
 * Provides tab creation, switching, and lifecycle management
 */

import { AppEventBus } from '@modules/core/event-bus';
import { WebEngineService } from '@modules/webengine/webengine-service';
import { AppManagerService } from '@modules/appmanager/appmanager-service';
import { TabInfo, CreateTabRequest, TabManagerState, SessionMode } from '@shared/types/app.types';
import { TabFaviconUpdatedEvent } from '@shared/types/events.types';
import { BrowserWindow } from 'electron';

export class TabManagerService {
  private readonly webEngineService: WebEngineService;
  private readonly appManagerService: AppManagerService;
  private readonly logger: Console;

  private tabs = new Map<string, TabInfo>();
  private activeTabId: string | null = null;
  private maxTabs = 10;
  private isInitialized = false;

  constructor(
    private eventBus: AppEventBus,
    webEngineService: WebEngineService,
    appManagerService: AppManagerService,
    logger: Console = console
  ) {
    this.webEngineService = webEngineService;
    this.appManagerService = appManagerService;
    this.logger = logger;

    // Subscribe to favicon update events
    this.setupEventHandlers();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Verify dependencies are available
      if (!this.webEngineService) {
        throw new Error('WebEngineService dependency is not available');
      }

      if (!this.appManagerService) {
        throw new Error('AppManagerService dependency is not available');
      }

      await this.loadExistingTabs();

      this.isInitialized = true;

    } catch (error) {
      this.logger.error('Failed to initialize Tab Manager Service:', error);
      throw error;
    }
  }

  async start(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('TabManagerService must be initialized before starting');
    }
  }

  async stop(): Promise<void> {
    this.isInitialized = false;
  }

  async createTab(request: CreateTabRequest): Promise<TabInfo> {
    this.logger.info('Creating new tab:', request);

    // Validate initialization
    if (!this.isInitialized) {
      throw new Error('TabManagerService is not initialized');
    }

    // Validate dependencies
    if (!this.webEngineService) {
      throw new Error('WebEngineService is not available');
    }

    if (!this.appManagerService) {
      throw new Error('AppManagerService is not available');
    }

    if (this.tabs.size >= this.maxTabs) {
      throw new Error(`Maximum number of tabs (${this.maxTabs}) reached`);
    }

    // Validate request with better error messages
    if (!request) {
      throw new Error('CreateTabRequest is required');
    }

    if (!request.url || typeof request.url !== 'string' || !request.url.trim()) {
      throw new Error('Valid URL is required for tab creation');
    }

    if (!request.name || typeof request.name !== 'string' || !request.name.trim()) {
      throw new Error('Valid name is required for tab creation');
    }

    // Enhanced URL validation and auto-correction
    let validatedUrl = request.url.trim();

    // Auto-correct common URL issues
    if (!validatedUrl.startsWith('http://') && !validatedUrl.startsWith('https://')) {
      // Add https:// if no protocol specified
      validatedUrl = 'https://' + validatedUrl;
      this.logger.debug(`Auto-corrected URL from "${request.url}" to "${validatedUrl}"`);
    }

    // Validate URL format after correction
    try {
      const urlObj = new URL(validatedUrl);
      // Ensure it's a valid web URL (http or https)
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        throw new Error(`Unsupported protocol: ${urlObj.protocol}. Only HTTP and HTTPS are supported.`);
      }
    } catch (urlError) {
      throw new Error(`Invalid URL format: ${validatedUrl}. Please enter a valid web address.`);
    }

    try {
      // Create a new WebApp for the tab with validated URL
      const appRequest = {
        name: request.name.trim(),
        url: validatedUrl, // Use the validated and corrected URL
        sessionMode: request.sessionMode || SessionMode.Isolated
      };

      this.logger.info('Creating app with request:', appRequest);

      // Step 1: Create the app
      let app;
      try {
        app = await this.appManagerService.createApp(appRequest);
        this.logger.info(`App created successfully: ${app.id}`);
      } catch (appError) {
        this.logger.error('Failed to create app:', appError);
        throw new Error(`Failed to create app: ${appError instanceof Error ? appError.message : String(appError)}`);
      }

      // Step 2: Create WebAppHost for the tab
      try {
        await this.webEngineService.createWebAppHost(app);
        this.logger.info(`WebAppHost created successfully for app: ${app.id}`);
      } catch (hostError) {
        this.logger.error('Failed to create WebAppHost:', hostError);
        
        // Cleanup: delete the app we just created since WebAppHost creation failed
        try {
          await this.appManagerService.deleteApp(app.id);
          this.logger.info('Cleaned up app after WebAppHost creation failure');
        } catch (cleanupError) {
          this.logger.error('Failed to cleanup app after WebAppHost creation failure:', cleanupError);
        }
        
        throw new Error(`Failed to create WebAppHost: ${hostError instanceof Error ? hostError.message : String(hostError)}`);
      }

      // Step 3: Validate app has valid ID
      if (!app.id || typeof app.id !== 'string') {
        throw new Error(`App created with invalid ID: ${app.id}`);
      }

      // Step 4: Create tab info
      const tabInfo: TabInfo = {
        id: app.id,
        title: app.name,
        url: app.url,
        isActive: false,
        isLoading: false,
        canGoBack: false,
        canGoForward: false
      };

      if (app.iconPath) {
        tabInfo.favicon = app.iconPath;
      }

      // Step 5: Store tab
      this.tabs.set(app.id, tabInfo);
      this.logger.info(`Tab stored in map: ${app.id}`);

      // Step 5: Activate the new tab if it's the first one or if requested
      if (this.tabs.size === 1 || request.activate === true) {
        try {
          await this.activateTab(app.id);
          this.logger.info(`Tab activated: ${app.id}`);
        } catch (activateError) {
          this.logger.warn('Failed to auto-activate new tab:', activateError);
          // Don't throw here - tab creation was successful, activation failure is not critical
        }
      }

      this.logger.info(`Tab created successfully: ${app.id} (${tabInfo.title})`);
      return tabInfo;

    } catch (error) {
      this.logger.error('Failed to create tab:', error);
      throw error;
    }
  }

  async closeTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    try {
      // Remove WebAppHost
      await this.webEngineService.removeWebAppHost(tabId);

      // Delete the app
      await this.appManagerService.deleteApp(tabId);

      // Remove from tabs
      this.tabs.delete(tabId);

      // If this was the active tab, activate another one
      if (this.activeTabId === tabId) {
        this.activeTabId = null;

        // Activate the first available tab
        const remainingTabs = Array.from(this.tabs.keys());
        if (remainingTabs.length > 0 && remainingTabs[0]) {
          await this.activateTab(remainingTabs[0]);
        }
      }

    } catch (error) {
      this.logger.error(`Failed to close tab ${tabId}:`, error);
      throw error;
    }
  }

  async activateTab(tabId: string): Promise<void> {
    // Validate initialization
    if (!this.isInitialized) {
      throw new Error('TabManagerService is not initialized');
    }

    // Validate tabId parameter
    if (!tabId || typeof tabId !== 'string') {
      throw new Error(`Invalid tab ID provided: ${tabId}`);
    }

    const tab = this.tabs.get(tabId);
    if (!tab) {
      this.logger.error(`Tab activation failed - tab not found: ${tabId}`);
      this.logger.error(`Available tabs: ${Array.from(this.tabs.keys()).join(', ')}`);
      throw new Error(`Tab ${tabId} not found`);
    }

    // Prevent multiple simultaneous activations
    if (tab.isActive && this.activeTabId === tabId) {
      return;
    }

    try {
      // Step 1: Deactivate current tab
      if (this.activeTabId && this.activeTabId !== tabId) {
        const currentTab = this.tabs.get(this.activeTabId);
        if (currentTab) {
          currentTab.isActive = false;
        }
      }

      // Step 2: Activate new tab in local state
      tab.isActive = true;
      this.activeTabId = tabId;

      // Step 3: Verify WebAppHost exists before switching
      const webAppHost = this.webEngineService.getWebAppHost(tabId);
      if (!webAppHost) {
        throw new Error(`WebAppHost for tab ${tabId} not found`);
      }

      // Step 4: Switch WebAppHost with enhanced error handling
      await this.webEngineService.switchToWebApp(tabId);

      // Step 5: Ensure content is loaded (but don't rely on this for attachment)
      await this.ensureTabContentLoaded(tabId, webAppHost, tab);

    } catch (error) {
      // Revert local state on error
      if (tab) {
        tab.isActive = false;
      }

      // Restore previous active tab if possible
      const previousActiveTab = Array.from(this.tabs.values()).find(t => t.id !== tabId && t.isActive);
      if (previousActiveTab) {
        this.activeTabId = previousActiveTab.id;
      } else {
        this.activeTabId = null;
      }

      throw error;
    }
  }

  private async ensureTabContentLoaded(_tabId: string, webAppHost: any, tab: any): Promise<void> {
    try {
      // Ensure the web app is navigated to its URL if not already loaded
      if (!webAppHost.CurrentUrl && tab.url) {
        try {
          await webAppHost.navigate(tab.url);
        } catch (navError) {
          // Don't throw here - tab should still be usable
        }
      }

      // Force immediate UI refresh with better error handling
      setTimeout(async () => {
        try {
          if (webAppHost.BrowserView && webAppHost.BrowserView.webContents) {
            // Only reload if the BrowserView and webContents are still valid
            await webAppHost.BrowserView.webContents.reload();
          }
        } catch (refreshError) {
          // Don't throw here - this is a nice-to-have optimization
        }
      }, 100); // Small delay to ensure tab switch is complete

    } catch (error) {
      // Don't throw here - tab activation should still succeed
    }
  }

  async navigateTab(tabId: string, url: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    try {
      await this.webEngineService.navigateWebApp(tabId, url);
      tab.url = url;
      tab.isLoading = true;

    } catch (error) {
      this.logger.error(`Failed to navigate tab ${tabId} to ${url}:`, error);
      throw error;
    }
  }

  async reloadTab(tabId: string): Promise<void> {
    const tab = this.tabs.get(tabId);
    if (!tab) {
      throw new Error(`Tab ${tabId} not found`);
    }

    try {
      await this.webEngineService.reloadWebApp(tabId);
      tab.isLoading = true;

    } catch (error) {
      this.logger.error(`Failed to reload tab ${tabId}:`, error);
      throw error;
    }
  }

  getAllTabs(): TabInfo[] {
    return Array.from(this.tabs.values());
  }

  getActiveTab(): TabInfo | null {
    if (!this.activeTabId) {
      return null;
    }
    return this.tabs.get(this.activeTabId) || null;
  }

  getTabManagerState(): TabManagerState {
    return {
      tabs: this.getAllTabs(),
      activeTabId: this.activeTabId,
      maxTabs: this.maxTabs
    };
  }

  private setupEventHandlers(): void {
    // Subscribe to favicon update events
    this.eventBus.subscribe('WebAppFaviconUpdated', (event: any) => {
      this.onFaviconUpdated(event.appId, event.faviconUrl);
    });

    // Subscribe to new window events
    this.eventBus.subscribe('WebAppNewWindowRequested', (event: any) => {
      this.onNewWindowRequested(event.appId, event.url, event.frameName);
    });

    // Subscribe to navigation completion events
    this.eventBus.subscribe('WebAppNavigationCompleted', (event: any) => {
      this.onNavigationCompleted(event.appId, event.url, event.isSuccess);
    });

    // Subscribe to title change events
    this.eventBus.subscribe('WebAppTitleChanged', (event: any) => {
      this.onTitleChanged(event.appId, event.title);
    });

    // Subscribe to loading state change events
    this.eventBus.subscribe('WebAppLoadingStateChanged', (event: any) => {
      this.onLoadingStateChanged(event.appId, event.isLoading);
    });
  }

  private onFaviconUpdated(appId: string, faviconUrl: string): void {
    const tab = this.tabs.get(appId);
    if (tab) {
      tab.favicon = faviconUrl;
      this.logger.debug(`Updated favicon for tab ${appId}: ${faviconUrl}`);

      // Emit IPC event to renderer for immediate UI update
      const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
      if (mainWindow) {
        mainWindow.webContents.send('tab-favicon-changed', {
          tabId: appId,
          faviconUrl: faviconUrl
        });
      }

      // Also emit internal event for other services
      this.eventBus.publish(new TabFaviconUpdatedEvent(appId, faviconUrl));
    }
  }

  private async onNewWindowRequested(appId: string, url: string, frameName?: string): Promise<void> {
    this.logger.debug(`New window requested from tab ${appId}: ${url} (frame: ${frameName})`);

    try {
      // Extract domain from URL for tab name
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace('www.', '');
      const tabName = frameName || `${domain} - New Tab`;

      // Create new tab with the popup URL
      const newTabRequest: CreateTabRequest = {
        url: url,
        name: tabName,
        sessionMode: SessionMode.Shared, // Use shared session for popup links
        activate: true // Activate the new tab immediately
      };

      const newTab = await this.createTab(newTabRequest);
      this.logger.info(`Created new tab for popup: ${newTab.id} - ${newTab.title}`);

      // Emit IPC event to notify renderer of new tab creation
      const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
      if (mainWindow) {
        mainWindow.webContents.send('tab-created', newTab);
      }

    } catch (error) {
      this.logger.error(`Failed to create tab for popup ${url}:`, error);

      // Emit error notification to renderer
      const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
      if (mainWindow) {
        mainWindow.webContents.send('notification', {
          type: 'error',
          message: 'Failed to open popup link in new tab'
        });
      }
    }
  }

  private onNavigationCompleted(appId: string, url: string, isSuccess: boolean): void {
    const tab = this.tabs.get(appId);
    if (tab) {
      // Update tab URL and loading state
      if (isSuccess) {
        tab.url = url;
        tab.isLoading = false;
        this.logger.debug(`Updated tab ${appId} URL to: ${url}`);
      } else {
        tab.isLoading = false;
        this.logger.warn(`Navigation failed for tab ${appId}: ${url}`);
      }

      // Emit IPC event to renderer for immediate UI update
      const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
      if (mainWindow) {
        mainWindow.webContents.send('tab-navigation-completed', {
          tabId: appId,
          url: url,
          isSuccess: isSuccess,
          isLoading: false
        });
      }
    }
  }

  private onTitleChanged(appId: string, title: string): void {
    const tab = this.tabs.get(appId);
    if (tab) {
      // Update tab title
      tab.title = title;
      this.logger.debug(`Updated tab ${appId} title to: ${title}`);

      // Emit IPC event to renderer for immediate UI update
      const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
      if (mainWindow) {
        mainWindow.webContents.send('tab-title-changed', {
          tabId: appId,
          title: title
        });
      }
    }
  }

  private onLoadingStateChanged(appId: string, isLoading: boolean): void {
    const tab = this.tabs.get(appId);
    if (tab) {
      // Update tab loading state
      tab.isLoading = isLoading;
      this.logger.debug(`Updated tab ${appId} loading state to: ${isLoading}`);

      // Emit IPC event to renderer for immediate UI update
      const mainWindow = BrowserWindow.getAllWindows().find(win => !win.isDestroyed());
      if (mainWindow) {
        mainWindow.webContents.send('tab-loading-state-changed', {
          tabId: appId,
          isLoading: isLoading
        });
      }
    }
  }

  private async loadExistingTabs(): Promise<void> {
    try {
      // Convert existing apps to tabs
      const apps = await this.appManagerService.getAllApps();

      for (const app of apps) {
        const tabInfo: TabInfo = {
          id: app.id,
          title: app.name,
          url: app.url,
          isActive: app.isActive,
          isLoading: false,
          canGoBack: false,
          canGoForward: false
        };

        if (app.iconPath) {
          tabInfo.favicon = app.iconPath;
        }

        this.tabs.set(app.id, tabInfo);

        if (app.isActive) {
          this.activeTabId = app.id;
        }
      }

      this.logger.info(`Loaded ${this.tabs.size} existing tabs`);

    } catch (error) {
      this.logger.error('Failed to load existing tabs:', error);
      // Don't throw - this shouldn't prevent initialization
    }
  }
}
