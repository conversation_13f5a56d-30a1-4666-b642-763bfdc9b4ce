/**
 * Renderer process for the panel window
 * Handles UI interactions and communicates with main process
 */

import { WebAppModel, TabInfo } from '@shared/types/app.types';

class PanelRenderer {
  private apps: WebAppModel[] = [];
  private activeAppId: string | null = null;
  private isPinned = false;

  // Tab management
  private tabs: TabInfo[] = [];
  private activeTabId: string | null = null;

  // DOM elements
  private appListElement!: HTMLElement;
  private noAppsMessage!: HTMLElement;
  private webContentElement!: HTMLElement;
  private loadingIndicator!: HTMLElement;
  private pinButton!: HTMLButtonElement;
  private settingsButton!: HTMLButtonElement;
  private closeButton!: HTMLButtonElement;
  private addAppButton!: HTMLButtonElement;

  // Tab DOM elements
  private tabStripIcons!: HTMLElement;
  private tabStripContent!: HTMLElement;
  private tabStrip!: HTMLElement;
  private tabStripFlyout!: HTMLElement;
  private newTabButton!: HTMLButtonElement;

  // Flyout state management
  private flyoutVisible = false;
  private showTimer: NodeJS.Timeout | null = null;
  private hideTimer: NodeJS.Timeout | null = null;

  // Modal state (used for preventing duplicate dialogs and cleanup)
  // @ts-ignore - Used for state tracking and cleanup
  private settingsDialog: HTMLElement | null = null;

  constructor() {
    console.log('PanelRenderer constructor called');
    this.initializeDOM();
    this.setupEventHandlers();
    this.setupThemeHandlers();

    // Delay initial data loading to ensure everything is ready
    setTimeout(() => {
      this.loadInitialData();
    }, 100);
  }

  private initializeDOM(): void {
    this.appListElement = document.getElementById('app-list')!;
    this.noAppsMessage = document.getElementById('no-apps-message')!;
    this.webContentElement = document.getElementById('web-content')!;
    this.loadingIndicator = document.getElementById('loading-indicator')!;
    this.pinButton = document.getElementById('pin-button') as HTMLButtonElement;
    this.settingsButton = document.getElementById('settings-button') as HTMLButtonElement;
    this.closeButton = document.getElementById('close-button') as HTMLButtonElement;
    this.addAppButton = document.getElementById('add-app-button') as HTMLButtonElement;

    // Tab elements
    this.tabStripIcons = document.getElementById('tab-strip-icons')!;
    this.tabStripContent = document.getElementById('tab-strip-content')!;
    this.tabStrip = document.getElementById('tab-strip')!;
    this.tabStripFlyout = document.getElementById('tab-strip-flyout')!;
    this.newTabButton = document.getElementById('new-tab-button') as HTMLButtonElement;

    // Setup flyout hover management
    this.setupFlyoutManagement();
  }

  private setupEventHandlers(): void {
    // Panel controls
    this.pinButton.addEventListener('click', () => this.togglePin());
    this.settingsButton.addEventListener('click', () => this.openSettings());
    this.closeButton.addEventListener('click', () => this.closePanel());
    this.addAppButton.addEventListener('click', () => this.showAddAppDialog());

    // Tab controls
    this.newTabButton.addEventListener('click', () => this.createNewTab());

    // Listen for events from main process
    window.sideView.on('app-created', (app: WebAppModel) => {
      this.apps.push(app);
      this.renderAppList();
    });

    window.sideView.on('app-updated', (app: WebAppModel) => {
      const index = this.apps.findIndex(a => a.id === app.id);
      if (index >= 0) {
        this.apps[index] = app;
        this.renderAppList();
      }
    });

    window.sideView.on('app-deleted', (appId: string) => {
      this.apps = this.apps.filter(a => a.id !== appId);
      if (this.activeAppId === appId) {
        this.activeAppId = null;
        this.clearWebContent();
      }
      this.renderAppList();
    });

    // Tab events
    window.sideView.on('tab-created', (tab: TabInfo) => {
      console.log('[TAB-CREATED] New tab created via IPC:', tab);
      this.handleNewTabCreated(tab);
    });

    window.sideView.on('tab-closed', (tabId: string) => {
      this.tabs = this.tabs.filter(tab => tab.id !== tabId);
      this.renderTabs();
    });

    window.sideView.on('tab-activated', (tabId: string) => {
      this.setActiveTab(tabId);
    });

    window.sideView.on('app-activated', (appId: string) => {
      this.setActiveApp(appId);
    });

    // Favicon update events
    window.sideView.on('tab-favicon-changed', (data: { tabId: string, faviconUrl: string }) => {
      console.log(`[FAVICON-CHANGED] Received IPC message - Updating favicon for tab ${data.tabId}: ${data.faviconUrl}`);
      this.updateTabFavicon(data.tabId, data.faviconUrl);
      // Show a brief visual indicator that favicon was updated
      this.showNotification(`Favicon updated for tab ${data.tabId}`, 'info');
    });

    // Title change events
    window.sideView.on('tab-title-changed', (data: { tabId: string, title: string }) => {
      console.log(`[TITLE-CHANGED] Updating title for tab ${data.tabId}: ${data.title}`);
      this.updateTabTitle(data.tabId, data.title);
    });

    // Navigation completion events
    window.sideView.on('tab-navigation-completed', (data: { tabId: string, url: string, isSuccess: boolean, isLoading: boolean }) => {
      console.log(`[NAVIGATION-COMPLETED] Tab ${data.tabId} navigated to ${data.url}, success: ${data.isSuccess}`);
      this.updateTabNavigation(data.tabId, data.url, data.isSuccess, data.isLoading);
    });

    // Loading state change events
    window.sideView.on('tab-loading-state-changed', (data: { tabId: string, isLoading: boolean }) => {
      console.log(`[LOADING-STATE-CHANGED] Tab ${data.tabId} loading state: ${data.isLoading}`);
      this.updateTabLoadingState(data.tabId, data.isLoading);
    });
  }

  private setupThemeHandlers(): void {
    // Listen for theme changes from the main process
    window.addEventListener('theme-changed', (event: any) => {
      const { theme, isDark, source } = event.detail;
      console.log(`Theme changed: ${theme} (dark: ${isDark}, source: ${source})`);

      // Theme variables are already applied by the UIService
      // We can add any additional renderer-specific theme handling here
      this.updateThemeClasses(isDark);
    });

    // Load initial theme
    this.loadInitialTheme();
  }

  private async loadInitialTheme(): Promise<void> {
    try {
      const themeData = await window.sideView.theme?.get();
      if (themeData) {
        this.updateThemeClasses(themeData.isDark);
      }
    } catch (error) {
      console.warn('Failed to load initial theme:', error);
    }
  }

  private updateThemeClasses(isDark: boolean): void {
    // Update body classes for theme-specific styling
    document.body.classList.remove('theme-light', 'theme-dark');
    document.body.classList.add(isDark ? 'theme-dark' : 'theme-light');

    // Update any theme-dependent elements
    this.updateControlButtonIcons(isDark);
  }

  private updateControlButtonIcons(_isDark: boolean): void {
    // Update button icons based on theme if needed
    // For now, the emoji icons work well in both themes
  }

  private setupFlyoutManagement(): void {
    // Enhanced debounced hover logic for stable flyout behavior

    // Handle mouse enter on tab strip
    this.tabStrip.addEventListener('mouseenter', () => {
      this.onTabStripEnter();
    });

    // Handle mouse leave on tab strip
    this.tabStrip.addEventListener('mouseleave', () => {
      this.onTabStripLeave();
    });

    // Handle mouse enter on flyout
    this.tabStripFlyout.addEventListener('mouseenter', () => {
      this.onFlyoutEnter();
    });

    // Handle mouse leave on flyout
    this.tabStripFlyout.addEventListener('mouseleave', () => {
      this.onFlyoutLeave();
    });
  }

  private onTabStripEnter(): void {
    // Clear any pending hide timer
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }

    // If flyout is already visible, don't restart the show timer
    if (this.flyoutVisible) {
      return;
    }

    // Clear any existing show timer
    if (this.showTimer) {
      clearTimeout(this.showTimer);
    }

    // Start show timer with 100ms delay to prevent accidental triggers
    this.showTimer = setTimeout(() => {
      this.showFlyout();
      this.showTimer = null;
    }, 100);
  }

  private onTabStripLeave(): void {
    // Clear any pending show timer
    if (this.showTimer) {
      clearTimeout(this.showTimer);
      this.showTimer = null;
    }

    // If flyout is not visible, nothing to hide
    if (!this.flyoutVisible) {
      return;
    }

    // Clear any existing hide timer
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
    }

    // Start hide timer with 150ms delay to allow smooth transitions
    this.hideTimer = setTimeout(() => {
      this.hideFlyout();
      this.hideTimer = null;
    }, 150);
  }

  private onFlyoutEnter(): void {
    // Clear any pending hide timer - mouse is now over flyout
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }

    // If flyout is not visible yet, show it immediately
    if (!this.flyoutVisible) {
      // Clear any pending show timer
      if (this.showTimer) {
        clearTimeout(this.showTimer);
        this.showTimer = null;
      }
      this.showFlyout();
    }
  }

  private onFlyoutLeave(): void {
    // Clear any pending show timer
    if (this.showTimer) {
      clearTimeout(this.showTimer);
      this.showTimer = null;
    }

    // If flyout is not visible, nothing to hide
    if (!this.flyoutVisible) {
      return;
    }

    // Clear any existing hide timer
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
    }

    // Start hide timer with 150ms delay
    this.hideTimer = setTimeout(() => {
      this.hideFlyout();
      this.hideTimer = null;
    }, 150);
  }

  private async showFlyout(): Promise<void> {
    if (this.flyoutVisible) return;

    try {
      // Shrink BrowserView to allow flyout to be visible
      await window.sideView.panel.shrinkBrowserViewForOverlay();

      // Show flyout with smooth transition
      this.tabStrip.classList.add('flyout-visible');
      this.tabStripFlyout.classList.add('flyout-visible');
      this.flyoutVisible = true;

      console.log('Flyout shown');
    } catch (error) {
      console.error('Failed to show flyout:', error);
    }
  }

  private async hideFlyout(): Promise<void> {
    if (!this.flyoutVisible) return;

    try {
      // Hide flyout
      this.tabStrip.classList.remove('flyout-visible');
      this.tabStripFlyout.classList.remove('flyout-visible');
      this.flyoutVisible = false;

      // Restore BrowserView to normal size
      await window.sideView.panel.restoreBrowserViewFromOverlay();

      console.log('Flyout hidden');
    } catch (error) {
      console.error('Failed to hide flyout:', error);
    }
  }

  private async loadInitialData(): Promise<void> {
    try {
      console.log('Loading initial data...');

      // Check if window.sideView is available
      if (!window.sideView) {
        console.error('window.sideView is not available! Preload script may not have loaded.');
        this.showError('Failed to initialize SideView API');
        return;
      }

      console.log('window.sideView is available:', Object.keys(window.sideView));

      // Load apps
      console.log('Requesting apps from main process...');
      try {
        this.apps = await window.sideView.apps.getAll();
        console.log('Received apps:', this.apps);
      } catch (appsError) {
        console.error('Failed to get apps:', appsError);
        this.showError('Failed to load applications');
        return;
      }

      // Load pin state
      try {
        this.isPinned = await window.sideView.panel.isPinned();
        this.updatePinButton();
      } catch (pinError) {
        console.error('Failed to get pin state:', pinError);
        // Continue anyway, this is not critical
      }

      // Load tabs
      try {
        this.tabs = await window.sideView.tabs.getAll();
        console.log('Loaded tabs:', this.tabs);
      } catch (tabsError) {
        console.error('Failed to load tabs:', tabsError);
        // Continue anyway, this is not critical
      }

      // Ensure tab bar is visible
      this.ensureTabBarVisibility();

      // Render UI
      this.renderTabs();
      this.renderAppList();

      // Activate first tab if available
      if (this.tabs.length > 0 && !this.activeTabId) {
        const activeTab = this.tabs.find(tab => tab.isActive) || this.tabs[0];
        if (activeTab && activeTab.id && typeof activeTab.id === 'string') {
          console.log('Activating tab:', activeTab.title, 'ID:', activeTab.id);
          await this.activateTab(activeTab.id);
        } else if (activeTab) {
          console.error('Found tab but it has invalid ID:', activeTab);
          this.showNotification('Tab has invalid ID', 'error');
        }
      } else if (this.apps.length > 0 && !this.activeAppId) {
        // Fallback to apps if no tabs
        const firstApp = this.apps[0];
        if (firstApp) {
          console.log('Activating first app:', firstApp.name);
          await this.activateApp(firstApp.id);
        }
      } else {
        console.log('No tabs or apps available to activate');
        this.showEmptyState();
      }

    } catch (error) {
      console.error('Failed to load initial data:', error);
      this.showError('Failed to initialize panel');
    }
  }

  private ensureTabBarVisibility(): void {
    const tabBar = document.getElementById('tab-bar');
    if (tabBar) {
      // Always show tab bar - it's part of the core UI
      tabBar.style.display = 'flex';
      console.log('Tab bar visibility ensured - tab bar is now visible');

      // Also ensure the tabs container is visible
      const tabsContainer = document.getElementById('tabs-container');
      if (tabsContainer) {
        tabsContainer.style.display = 'flex';
        console.log('Tabs container visibility ensured');
      }

      // Ensure new tab button is visible
      const newTabButton = document.getElementById('new-tab-button');
      if (newTabButton) {
        newTabButton.style.display = 'flex';
        console.log('New tab button visibility ensured');
      }
    } else {
      console.error('Tab bar element not found in DOM!');
    }
  }

  private showEmptyState(): void {
    this.clearWebContent();
    // If no tabs exist, create a default tab
    if (this.tabs.length === 0) {
      console.log('No tabs found, creating default tab');
      this.createNewTab();
    }
  }

  private renderAppList(): void {
    if (this.apps.length === 0) {
      this.noAppsMessage.style.display = 'block';
      return;
    }

    this.noAppsMessage.style.display = 'none';

    const appItems = this.apps.map(app => this.createAppItem(app));
    this.appListElement.innerHTML = '';
    appItems.forEach(item => this.appListElement.appendChild(item));
  }

  private createAppItem(app: WebAppModel): HTMLElement {
    const item = document.createElement('div');
    item.className = `app-item ${app.id === this.activeAppId ? 'active' : ''}`;
    item.addEventListener('click', () => this.activateApp(app.id));

    const icon = document.createElement('div');
    icon.className = 'app-icon';
    if (app.iconPath) {
      icon.innerHTML = `<img src="${app.iconPath}" alt="${app.name}" style="width: 100%; height: 100%; border-radius: 4px;">`;
    } else {
      icon.textContent = app.name.charAt(0).toUpperCase();
    }

    const info = document.createElement('div');
    info.className = 'app-info';

    const name = document.createElement('div');
    name.className = 'app-name';
    name.textContent = app.name;

    const url = document.createElement('div');
    url.className = 'app-url';
    url.textContent = app.url;

    info.appendChild(name);
    info.appendChild(url);

    item.appendChild(icon);
    item.appendChild(info);

    // Add badge if app has notifications
    if (app.hasBadge && app.badgeCount && app.badgeCount > 0) {
      const badge = document.createElement('div');
      badge.className = 'app-badge';
      badge.textContent = app.badgeCount > 99 ? '99+' : app.badgeCount.toString();
      item.appendChild(badge);
    }

    return item;
  }

  private async activateApp(appId: string): Promise<void> {
    try {
      await window.sideView.apps.activate(appId);
      this.setActiveApp(appId);
    } catch (error) {
      console.error('Failed to activate app:', error);
    }
  }

  private setActiveApp(appId: string): void {
    this.activeAppId = appId;
    this.renderAppList(); // Re-render to update active state
    this.loadWebContent(appId);
  }

  private async loadWebContent(appId: string): Promise<void> {
    console.log('Loading web content for app:', appId);
    const app = this.apps.find(a => a.id === appId);
    if (!app) {
      console.error('App not found:', appId);
      return;
    }

    console.log('Loading app:', app.name, 'URL:', app.url);
    this.showLoading();

    try {
      // Activate the app in the main process - this will attach the BrowserView
      await window.sideView.apps.activate(appId);

      // Clear the web content area since BrowserView will overlay it
      this.webContentElement.innerHTML = '<div class="web-view-placeholder">Web content is loading...</div>';

      // Hide loading after a short delay to allow BrowserView to attach
      setTimeout(() => {
        this.hideLoading();
      }, 500);

      // Update last accessed time
      app.lastAccessed = new Date();

      console.log('Web app activated successfully:', app.name);

    } catch (error) {
      this.hideLoading();
      this.showError('Failed to load web application');
      console.error('Error loading web content:', error);
    }
  }

  private clearWebContent(): void {
    this.webContentElement.innerHTML = '<div class="loading-indicator">Select an app to view</div>';
  }

  private showLoading(): void {
    this.loadingIndicator.style.display = 'block';
  }

  private hideLoading(): void {
    this.loadingIndicator.style.display = 'none';
  }

  private showError(message: string): void {
    this.webContentElement.innerHTML = `<div class="loading-indicator" style="color: #ff4444;">${message}</div>`;
  }

  private async togglePin(): Promise<void> {
    this.isPinned = !this.isPinned;
    await window.sideView.panel.setPinned(this.isPinned);
    this.updatePinButton();
  }

  private updatePinButton(): void {
    this.pinButton.classList.toggle('pinned', this.isPinned);
    this.pinButton.title = this.isPinned ? 'Unpin Panel' : 'Pin Panel';
  }

  private openSettings(): void {
    this.showSettingsDialog();
  }



  private async showSettingsDialog(): Promise<void> {
    // Check if settings dialog is already open to prevent duplicates
    const existingOverlay = document.querySelector('.settings-overlay');
    if (existingOverlay) {
      return;
    }

    // Notify main process that modal is opening (with improved timing)
    try {
      await window.sideView.panel.setModalState(true);
    } catch (error) {
      console.error('Failed to set modal state:', error);
      // Continue anyway - modal can still work without BrowserView detachment
    }

    // Create settings dialog overlay
    const overlay = document.createElement('div');
    overlay.className = 'settings-overlay';
    this.settingsDialog = overlay;
    overlay.innerHTML = `
      <div class="settings-dialog">
        <div class="settings-header">
          <h2>Settings</h2>
          <button class="close-settings" title="Close">✕</button>
        </div>
        <div class="settings-content">
          <div class="settings-section">
            <h3>Panel</h3>
            <div class="setting-item">
              <label for="panel-width">Panel Width:</label>
              <input type="range" id="panel-width" min="250" max="800" step="10">
              <span class="setting-value" id="panel-width-value">400px</span>
            </div>
            <div class="setting-item">
              <label for="panel-height">Panel Height:</label>
              <input type="range" id="panel-height" min="400" max="1200" step="20">
              <span class="setting-value" id="panel-height-value">600px</span>
            </div>
            <div class="setting-item">
              <label for="panel-position">Position:</label>
              <select id="panel-position">
                <option value="left">Left</option>
                <option value="right">Right</option>
              </select>
            </div>
            <div class="setting-item">
              <label for="auto-hide">Auto Hide:</label>
              <input type="checkbox" id="auto-hide">
            </div>
          </div>

          <div class="settings-section">
            <h3>Edge Activation</h3>
            <div class="setting-item">
              <label for="edge-enabled">Enable Left Edge:</label>
              <input type="checkbox" id="edge-enabled">
            </div>
            <div class="setting-item">
              <label for="edge-position">Position:</label>
              <select id="edge-position">
                <option value="top">Top</option>
                <option value="middle">Middle</option>
                <option value="bottom">Bottom</option>
                <option value="full">Full Edge</option>
              </select>
            </div>
            <div class="setting-item">
              <label for="edge-size">Size (% of edge):</label>
              <input type="range" id="edge-size" min="10" max="100" step="5">
              <span class="setting-value" id="edge-size-value">50%</span>
            </div>
            <div class="setting-item">
              <label for="edge-width">Width (pixels):</label>
              <input type="range" id="edge-width" min="1" max="20" step="1">
              <span class="setting-value" id="edge-width-value">4px</span>
            </div>
          </div>

          <div class="settings-section">
            <h3>Theme</h3>
            <div class="setting-item">
              <label for="theme">Theme:</label>
              <select id="theme">
                <option value="system">System</option>
                <option value="light">Light</option>
                <option value="dark">Dark</option>
              </select>
            </div>
          </div>

          <div class="settings-section">
            <h3>Hotkeys</h3>
            <div class="setting-item">
              <label for="hotkeys-enabled">Enable Hotkeys:</label>
              <input type="checkbox" id="hotkeys-enabled">
            </div>
            <div class="setting-item">
              <label for="toggle-hotkey">Toggle Panel:</label>
              <input type="text" id="toggle-hotkey" readonly placeholder="Click to set">
            </div>
          </div>
        </div>
        <div class="settings-footer">
          <button class="settings-btn cancel">Cancel</button>
          <button class="settings-btn save">Save</button>
        </div>
      </div>
    `;

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      .settings-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2147483647; /* Maximum z-index to ensure dialog appears above BrowserView */
        backdrop-filter: blur(2px); /* Add blur effect for better visual separation */
      }

      .settings-dialog {
        background: var(--panel-background);
        border: 1px solid var(--panel-border);
        border-radius: 8px;
        width: 500px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        position: relative;
        z-index: 2147483647; /* Maximum z-index to ensure dialog appears above BrowserView */
      }

      .settings-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid var(--panel-border);
      }

      .settings-header h2 {
        margin: 0;
        color: var(--text-primary);
        font-size: 18px;
      }

      .close-settings {
        background: none;
        border: none;
        color: var(--text-secondary);
        font-size: 16px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
      }

      .close-settings:hover {
        background: var(--app-item-hover);
      }

      .settings-content {
        padding: 20px;
      }

      .settings-section {
        margin-bottom: 24px;
      }

      .settings-section h3 {
        margin: 0 0 12px 0;
        color: var(--text-primary);
        font-size: 14px;
        font-weight: 600;
      }

      .setting-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 12px;
      }

      .setting-item label {
        color: var(--text-secondary);
        font-size: 13px;
        min-width: 120px;
      }

      .setting-item input, .setting-item select {
        background: var(--panel-background-secondary);
        border: 1px solid var(--panel-border);
        color: var(--text-primary);
        padding: 6px 8px;
        border-radius: 4px;
        font-size: 13px;
      }

      .setting-item input[type="range"] {
        flex: 1;
      }

      .setting-value {
        color: var(--text-secondary);
        font-size: 12px;
        min-width: 50px;
      }

      .settings-footer {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        padding: 16px 20px;
        border-top: 1px solid var(--panel-border);
      }

      .settings-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        font-size: 13px;
        cursor: pointer;
      }

      .settings-btn.cancel {
        background: var(--panel-background-secondary);
        color: var(--text-secondary);
      }

      .settings-btn.save {
        background: var(--accent-color);
        color: var(--button-text);
      }

      .settings-btn:hover {
        opacity: 0.8;
      }
    `;

    document.head.appendChild(style);
    document.body.appendChild(overlay);

    // Load current settings and setup handlers
    this.setupSettingsDialog(overlay);
  }

  private async setupSettingsDialog(overlay: HTMLElement): Promise<void> {
    try {
      // Load current settings with retry logic and defensive programming
      let settings;
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          settings = await window.sideView.config.get();

          // Validate settings structure
          if (settings && typeof settings === 'object') {
            // Merge settings with defaults to ensure all required properties exist
            const defaultSettings = this.getDefaultSettings();

            settings = {
              ui: {
                ...defaultSettings.ui,
                ...settings.ui,
                edgeActivation: {
                  ...defaultSettings.ui.edgeActivation,
                  ...settings.ui?.edgeActivation,
                  left: {
                    ...defaultSettings.ui.edgeActivation.left,
                    ...settings.ui?.edgeActivation?.left
                  },
                  right: {
                    ...defaultSettings.ui.edgeActivation.right,
                    ...settings.ui?.edgeActivation?.right
                  },
                  top: {
                    ...defaultSettings.ui.edgeActivation.top,
                    ...settings.ui?.edgeActivation?.top
                  },
                  bottom: {
                    ...defaultSettings.ui.edgeActivation.bottom,
                    ...settings.ui?.edgeActivation?.bottom
                  }
                }
              },
              webEngine: {
                ...defaultSettings.webEngine,
                ...settings.webEngine
              },
              notifications: {
                ...defaultSettings.notifications,
                ...settings.notifications
              },
              hotkeys: {
                ...defaultSettings.hotkeys,
                ...settings.hotkeys
              },
              updater: {
                ...defaultSettings.updater,
                ...settings.updater
              }
            };

            break;
          } else {
            throw new Error('Settings object is invalid or null');
          }
        } catch (error) {
          retryCount++;
          console.warn(`Settings load attempt ${retryCount} failed:`, error);
          if (retryCount >= maxRetries) {
            // Use default settings as fallback
            settings = this.getDefaultSettings();
            console.warn('Using default settings as fallback');
            break;
          }
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      if (!settings) {
        settings = this.getDefaultSettings();
        console.warn('Using default settings as final fallback');
      }

      // Validate settings structure and provide fallbacks
      const uiSettings = settings.ui || {};
      const edgeActivation = uiSettings.edgeActivation || {};
      const leftEdge = edgeActivation.left || {};
      const hotkeys = settings.hotkeys || {};

      // Populate form with proper null checks and fallbacks
      const panelWidth = overlay.querySelector('#panel-width') as HTMLInputElement;
      const panelWidthValue = overlay.querySelector('#panel-width-value') as HTMLElement;
      const panelHeight = overlay.querySelector('#panel-height') as HTMLInputElement;
      const panelHeightValue = overlay.querySelector('#panel-height-value') as HTMLElement;
      const panelPosition = overlay.querySelector('#panel-position') as HTMLSelectElement;
      const autoHide = overlay.querySelector('#auto-hide') as HTMLInputElement;
      const edgeEnabled = overlay.querySelector('#edge-enabled') as HTMLInputElement;
      const edgePosition = overlay.querySelector('#edge-position') as HTMLSelectElement;
      const edgeSize = overlay.querySelector('#edge-size') as HTMLInputElement;
      const edgeSizeValue = overlay.querySelector('#edge-size-value') as HTMLElement;
      const edgeWidth = overlay.querySelector('#edge-width') as HTMLInputElement;
      const edgeWidthValue = overlay.querySelector('#edge-width-value') as HTMLElement;
      const theme = overlay.querySelector('#theme') as HTMLSelectElement;
      const hotkeysEnabled = overlay.querySelector('#hotkeys-enabled') as HTMLInputElement;
      const toggleHotkey = overlay.querySelector('#toggle-hotkey') as HTMLInputElement;

      // Safe assignment with fallbacks
      const safeAssign = (element: HTMLInputElement | HTMLSelectElement | HTMLElement, getter: () => any, fallback: any, displaySuffix: string = '') => {
        try {
          const value = getter();
          if (value !== undefined && value !== null) {
            if (element instanceof HTMLInputElement) {
              element.value = typeof value === 'string' ? value : String(value);
            } else if (element instanceof HTMLSelectElement) {
              element.value = String(value);
            } else {
              element.textContent = `${value}${displaySuffix}`;
            }
          } else {
            // Use fallback
            if (element instanceof HTMLInputElement) {
              element.value = typeof fallback === 'string' ? fallback : String(fallback);
            } else if (element instanceof HTMLSelectElement) {
              element.value = String(fallback);
            } else {
              element.textContent = `${fallback}${displaySuffix}`;
            }
          }
        } catch (error) {
          console.warn('Error assigning value to element:', error);
          // Use fallback on any error
          if (element instanceof HTMLInputElement) {
            element.value = typeof fallback === 'string' ? fallback : String(fallback);
          } else if (element instanceof HTMLSelectElement) {
            element.value = String(fallback);
          } else {
            element.textContent = `${fallback}${displaySuffix}`;
          }
        }
      };

      // Apply values with fallbacks
      safeAssign(panelWidth, () => uiSettings.panelWidth, 280);
      safeAssign(panelWidthValue, () => uiSettings.panelWidth, 280, 'px');
      safeAssign(panelHeight, () => uiSettings.panelHeight, 600);
      safeAssign(panelHeightValue, () => uiSettings.panelHeight, 600, 'px');
      safeAssign(panelPosition, () => uiSettings.panelPosition, 'left');
      
      // Boolean assignments need special handling
      try {
        autoHide.checked = uiSettings.autoHide !== undefined ? uiSettings.autoHide : true;
      } catch (error) {
        console.warn('Error setting autoHide:', error);
        autoHide.checked = true;
      }
      
      try {
        edgeEnabled.checked = leftEdge.enabled !== undefined ? leftEdge.enabled : true;
      } catch (error) {
        console.warn('Error setting edgeEnabled:', error);
        edgeEnabled.checked = true;
      }
      
      safeAssign(edgePosition, () => leftEdge.position, 'middle');
      safeAssign(edgeSize, () => leftEdge.size, 50);
      safeAssign(edgeSizeValue, () => leftEdge.size, 50, '%');
      safeAssign(edgeWidth, () => leftEdge.width, 4);
      safeAssign(edgeWidthValue, () => leftEdge.width, 4, 'px');
      safeAssign(theme, () => uiSettings.theme, 'system');
      
      try {
        hotkeysEnabled.checked = hotkeys.enabled !== undefined ? hotkeys.enabled : true;
      } catch (error) {
        console.warn('Error setting hotkeysEnabled:', error);
        hotkeysEnabled.checked = true;
      }
      
      safeAssign(toggleHotkey, () => hotkeys.togglePanel, 'CommandOrControl+Alt+Right');

      // Setup event handlers
      panelWidth.addEventListener('input', () => {
        panelWidthValue.textContent = `${panelWidth.value}px`;
      });

      panelHeight.addEventListener('input', () => {
        panelHeightValue.textContent = `${panelHeight.value}px`;
      });

      edgeSize.addEventListener('input', () => {
        edgeSizeValue.textContent = `${edgeSize.value}%`;
      });

      edgeWidth.addEventListener('input', () => {
        edgeWidthValue.textContent = `${edgeWidth.value}px`;
      });

      // Close handlers
      const closeBtn = overlay.querySelector('.close-settings') as HTMLButtonElement;
      const cancelBtn = overlay.querySelector('.cancel') as HTMLButtonElement;

      const closeDialog = async () => {
        try {
          await window.sideView.panel.setModalState(false);
        } catch (error) {
          console.error('Failed to clear modal state:', error);
          // Continue with DOM cleanup anyway
        }

        // Clean up dialog elements
        try {
          if (overlay.parentNode) {
            document.body.removeChild(overlay);
          }

          // Clear the dialog reference
          this.settingsDialog = null;

        } catch (cleanupError) {
          console.error('Failed to clean up dialog elements:', cleanupError);
        }
      };

      closeBtn.addEventListener('click', closeDialog);
      cancelBtn.addEventListener('click', closeDialog);
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) closeDialog();
      });

      // Save handler
      const saveBtn = overlay.querySelector('.save') as HTMLButtonElement;
      saveBtn.addEventListener('click', async () => {
        try {
          // Validate inputs
          const widthValue = parseInt(panelWidth.value);
          const heightValue = parseInt(panelHeight.value);

          if (isNaN(widthValue) || widthValue < 250 || widthValue > 800) {
            this.showNotification('Panel width must be between 250 and 800 pixels', 'error');
            return;
          }

          if (isNaN(heightValue) || heightValue < 400 || heightValue > 1200) {
            this.showNotification('Panel height must be between 400 and 1200 pixels', 'error');
            return;
          }

          // Build updates object with safe property access
          const updates = {
            ui: {
              panelWidth: widthValue,
              panelHeight: heightValue,
              panelPosition: panelPosition.value as any,
              autoHide: autoHide.checked,
              theme: theme.value as any,
              animationDuration: uiSettings.animationDuration || 300,
              activationDelay: uiSettings.activationDelay || 500,
              edgeActivation: {
                ...edgeActivation,
                left: {
                  enabled: edgeEnabled.checked,
                  position: edgePosition.value as any,
                  size: parseInt(edgeSize.value) || 50,
                  width: parseInt(edgeWidth.value) || 4,
                  offset: leftEdge.offset || 0
                }
              }
            },
            hotkeys: {
              enabled: hotkeysEnabled.checked,
              togglePanel: toggleHotkey.value || 'CommandOrControl+Alt+Right',
              newTab: hotkeys.newTab || 'CommandOrControl+T',
              refresh: hotkeys.refresh || 'F5'
            }
          };

          await window.sideView.config.update(updates);
          closeDialog();

          // Show success message
          this.showNotification('Settings saved successfully', 'success');

          // Restart widget to ensure clean state after settings changes
          try {
            await window.sideView.panel.restartWidget();
            this.showNotification('Widget restarted for clean state', 'info');
          } catch (restartError) {
            console.warn('Failed to restart widget after settings save:', restartError);
            // Don't show error to user as settings were saved successfully
          }
        } catch (error) {
          console.error('Failed to save settings:', error);
          this.showNotification('Failed to save settings', 'error');
        }
      });

    } catch (error) {
      console.error('Failed to load settings:', error);
      this.showNotification('Failed to load settings - using defaults', 'error');

      // Close the dialog and show error state
      const closeDialog = async () => {
        try {
          await window.sideView.panel.setModalState(false);
        } catch (error) {
          console.error('Failed to clear modal state on error:', error);
        }

        if (overlay.parentNode) {
          document.body.removeChild(overlay);
        }
        this.settingsDialog = null;
      };

      // Add error message to dialog
      const errorDiv = document.createElement('div');
      errorDiv.style.cssText = `
        background: #ff4444;
        color: white;
        padding: 12px;
        margin: 16px;
        border-radius: 4px;
        font-size: 13px;
      `;
      errorDiv.textContent = `Settings loading failed: ${error instanceof Error ? error.message : String(error)}`;

      const dialogContent = overlay.querySelector('.settings-content');
      if (dialogContent) {
        dialogContent.insertBefore(errorDiv, dialogContent.firstChild);
      }

      // Auto-close after 5 seconds
      setTimeout(closeDialog, 5000);
    }
  }

  private getDefaultSettings(): any {
    return {
      ui: {
        panelWidth: 280,
        panelHeight: 600,
        panelPosition: 'right',
        theme: 'auto',
        animationDuration: 300,
        autoHide: true,
        activationDelay: 200,
        edgeActivation: {
          left: { enabled: false, position: 'middle', size: 50 },
          right: { enabled: true, position: 'middle', size: 50 },
          top: { enabled: false, position: 'middle', size: 50 },
          bottom: { enabled: false, position: 'middle', size: 50 }
        }
      },
      webEngine: {
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) SideView/1.0',
        enableDevTools: false,
        enableJavaScript: true,
        enableImages: true,
        enablePlugins: false,
        defaultZoomLevel: 1.0
      },
      notifications: {
        enabled: true,
        showBadges: true,
        soundEnabled: true,
        position: 'TopRight'
      },
      hotkeys: {
        togglePanel: 'CommandOrControl+Alt+Right',
        newTab: 'CommandOrControl+T',
        refresh: 'F5',
        enabled: true
      },
      updater: {
        enabled: true,
        checkInterval: 3600000,
        autoDownload: true,
        autoInstall: false,
        channel: 'Stable'
      }
    };
  }

  private showNotification(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    const style = document.createElement('style');
    style.textContent = `
      .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 4px;
        color: white;
        font-size: 13px;
        z-index: 2000;
        animation: slideIn 0.3s ease;
      }

      .notification-success { background: #4caf50; }
      .notification-error { background: #f44336; }
      .notification-info { background: #2196f3; }

      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `;

    document.head.appendChild(style);
    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  private async closePanel(): Promise<void> {
    await window.sideView.panel.hide();
  }

  private async showAddAppDialog(): Promise<void> {
    console.log('[RENDERER] Opening add app dialog');

    // Notify main process that modal is opening (same as settings dialog)
    try {
      await window.sideView.panel.setModalState(true);
    } catch (error) {
      console.error('Failed to set modal state:', error);
      // Continue anyway - modal can still work without BrowserView detachment
    }

    // Create a proper HTML dialog instead of using prompt()
    const dialog = this.createAddAppDialog();
    document.body.appendChild(dialog);

    // Focus on the first input
    const nameInput = dialog.querySelector('#app-name-input') as HTMLInputElement;
    if (nameInput) {
      nameInput.focus();
    }
  }

  private createAddAppDialog(): HTMLElement {
    const overlay = document.createElement('div');
    overlay.className = 'dialog-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2147483647; /* Maximum z-index to ensure dialog appears above BrowserView */
      pointer-events: auto;
      user-select: none;
    `;

    const dialog = document.createElement('div');
    dialog.className = 'add-app-dialog';
    dialog.style.cssText = `
      background: var(--bg-primary, #1e1e1e);
      border: 1px solid var(--border-color, #333);
      border-radius: 8px;
      padding: 20px;
      min-width: 300px;
      max-width: 400px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      position: relative;
      z-index: 2147483647;
      pointer-events: auto;
      user-select: text;
    `;

    dialog.innerHTML = `
      <h3 style="margin: 0 0 16px 0; color: var(--text-primary, #fff);">Add New App</h3>

      <div style="margin-bottom: 12px;">
        <label for="app-name-input" style="display: block; margin-bottom: 4px; color: var(--text-secondary, #ccc); font-size: 12px;">App Name</label>
        <input type="text" id="app-name-input" placeholder="Enter app name..." style="
          width: 100%;
          padding: 8px;
          border: 1px solid var(--border-color, #333);
          border-radius: 4px;
          background: var(--bg-secondary, #2d2d2d);
          color: var(--text-primary, #fff);
          font-size: 14px;
          box-sizing: border-box;
        ">
      </div>

      <div style="margin-bottom: 16px;">
        <label for="app-url-input" style="display: block; margin-bottom: 4px; color: var(--text-secondary, #ccc); font-size: 12px;">App URL</label>
        <input type="url" id="app-url-input" placeholder="https://example.com" style="
          width: 100%;
          padding: 8px;
          border: 1px solid var(--border-color, #333);
          border-radius: 4px;
          background: var(--bg-secondary, #2d2d2d);
          color: var(--text-primary, #fff);
          font-size: 14px;
          box-sizing: border-box;
        ">
      </div>

      <div style="display: flex; gap: 8px; justify-content: flex-end;">
        <button id="cancel-btn" style="
          padding: 8px 16px;
          border: 1px solid var(--border-color, #333);
          border-radius: 4px;
          background: var(--bg-secondary, #2d2d2d);
          color: var(--text-primary, #fff);
          cursor: pointer;
          font-size: 14px;
        ">Cancel</button>
        <button id="create-btn" style="
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          background: var(--accent-color, #007acc);
          color: white;
          cursor: pointer;
          font-size: 14px;
        ">Create App</button>
      </div>
    `;

    overlay.appendChild(dialog);

    // Event handlers
    const nameInput = dialog.querySelector('#app-name-input') as HTMLInputElement;
    const urlInput = dialog.querySelector('#app-url-input') as HTMLInputElement;
    const cancelBtn = dialog.querySelector('#cancel-btn') as HTMLButtonElement;
    const createBtn = dialog.querySelector('#create-btn') as HTMLButtonElement;

    const closeDialog = async () => {
      try {
        await window.sideView.panel.setModalState(false);
      } catch (error) {
        console.error('Failed to clear modal state:', error);
        // Continue with DOM cleanup anyway
      }

      // Clean up dialog elements
      try {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay);
        }
      } catch (cleanupError) {
        console.error('Failed to clean up dialog elements:', cleanupError);
      }
    };

    const createApp = async () => {
      const name = nameInput.value.trim();
      const url = urlInput.value.trim();

      if (!name) {
        this.showDialogError('App name is required');
        nameInput.focus();
        return;
      }

      if (!url) {
        this.showDialogError('App URL is required');
        urlInput.focus();
        return;
      }

      // Validate URL format
      try {
        new URL(url);
      } catch {
        // Try adding https:// if missing
        let correctedUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          correctedUrl = 'https://' + url;
          try {
            new URL(correctedUrl);
            urlInput.value = correctedUrl;
          } catch {
            this.showDialogError('Invalid URL format. Please enter a valid URL (e.g., https://example.com)');
            urlInput.focus();
            return;
          }
        } else {
          this.showDialogError('Invalid URL format. Please enter a valid URL (e.g., https://example.com)');
          urlInput.focus();
          return;
        }
      }

      console.log(`[RENDERER] Creating app: ${name} - ${url}`);
      await closeDialog();

      try {
        await window.sideView.apps.create({
          name: name,
          url: url,
          sessionMode: 'isolated' as any
        });

        console.log(`[RENDERER] App created successfully: ${name}`);
        this.showNotification(`App "${name}" created successfully`, 'success');

      } catch (error) {
        console.error('[RENDERER] Failed to create app:', error);

        // Log detailed error information
        if (error instanceof Error) {
          console.error('[RENDERER] Error name:', error.name);
          console.error('[RENDERER] Error message:', error.message);
          console.error('[RENDERER] Error stack:', error.stack);
        } else {
          console.error('[RENDERER] Error (non-Error object):', JSON.stringify(error, null, 2));
        }

        // Show user-friendly error message
        let userMessage = 'Failed to create app';
        if (error instanceof Error) {
          if (error.message.includes('App name is required')) {
            userMessage = 'App name is required';
          } else if (error.message.includes('App URL is required')) {
            userMessage = 'App URL is required';
          } else if (error.message.includes('Invalid URL format')) {
            userMessage = 'Invalid URL format. Please enter a valid URL (e.g., https://example.com)';
          } else if (error.message.includes('WebAppHost')) {
            userMessage = 'Failed to create web view for the app. Please try again.';
          } else {
            userMessage = `Failed to create app: ${error.message}`;
          }
        }

        this.showNotification(userMessage, 'error');
      }
    };

    // Event listeners
    cancelBtn.addEventListener('click', closeDialog);
    createBtn.addEventListener('click', createApp);

    // Close on overlay click
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        closeDialog();
      }
    });

    // Handle Enter key
    const handleEnter = (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        createApp();
      } else if (e.key === 'Escape') {
        closeDialog();
      }
    };

    nameInput.addEventListener('keydown', handleEnter);
    urlInput.addEventListener('keydown', handleEnter);

    return overlay;
  }

  private showDialogError(message: string): void {
    // Find existing error message and remove it
    const existingError = document.querySelector('.dialog-error-message');
    if (existingError) {
      existingError.remove();
    }

    // Create new error message
    const dialog = document.querySelector('.add-app-dialog');
    if (dialog) {
      const errorDiv = document.createElement('div');
      errorDiv.className = 'dialog-error-message';
      errorDiv.style.cssText = `
        color: #ff6b6b;
        font-size: 12px;
        margin-top: 8px;
        padding: 4px 8px;
        background: rgba(255, 107, 107, 0.1);
        border: 1px solid rgba(255, 107, 107, 0.3);
        border-radius: 4px;
      `;
      errorDiv.textContent = message;

      const buttonsDiv = dialog.querySelector('div:last-child');
      if (buttonsDiv) {
        dialog.insertBefore(errorDiv, buttonsDiv);
      }
    }
  }

  // Tab Management Methods
  private renderTabs(): void {
    // Clean up any existing timers before re-rendering
    this.cleanupFlyoutTimers();

    // Debug: Log tab state
    console.log(`[RENDER-TABS] Rendering ${this.tabs.length} tabs:`,
      this.tabs.map(t => ({ id: t.id, title: t.title, isActive: t.isActive })));

    // Clear both icon strip and flyout content
    this.tabStripIcons.innerHTML = '';
    this.tabStripContent.innerHTML = '';

    if (this.tabs.length === 0) {
      // Show a placeholder when no tabs exist
      const placeholder = document.createElement('div');
      placeholder.className = 'tabs-placeholder';
      placeholder.textContent = 'No tabs - click + to create';
      placeholder.style.cssText = `
        color: var(--text-secondary);
        font-size: 10px;
        padding: 4px;
        text-align: center;
        font-style: italic;
      `;
      this.tabStripIcons.appendChild(placeholder);
    } else {
      this.tabs.forEach(tab => {
        // Validate tab before rendering
        if (!tab.id || typeof tab.id !== 'string') {
          console.error('Skipping tab with invalid ID:', tab);
          return;
        }

        // Create tab icon for the strip
        const tabIcon = this.createTabIcon(tab);
        this.tabStripIcons.appendChild(tabIcon);

        // Create tab element for the flyout
        const tabElement = this.createTabElement(tab);
        this.tabStripContent.appendChild(tabElement);
      });
    }
  }

  private cleanupFlyoutTimers(): void {
    if (this.showTimer) {
      clearTimeout(this.showTimer);
      this.showTimer = null;
    }
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }
  }

  private updateTabFavicon(tabId: string, faviconUrl: string): void {
    // Update tab data
    const tab = this.tabs.find(t => t.id === tabId);
    if (tab) {
      tab.favicon = faviconUrl;
      console.log(`Updated favicon for tab ${tabId}: ${faviconUrl}`);

      // Update the favicon in both icon strip and flyout
      this.updateTabIconFavicon(tabId, faviconUrl);
      this.updateTabElementFavicon(tabId, faviconUrl);
    }
  }

  private updateTabIconFavicon(tabId: string, faviconUrl: string): void {
    console.log(`[FAVICON-UPDATE] Updating tab icon favicon for ${tabId}: ${faviconUrl}`);
    const tabIcon = this.tabStripIcons.querySelector(`[data-tab-id="${tabId}"]`) as HTMLElement;
    if (tabIcon) {
      console.log(`[FAVICON-UPDATE] Found tab icon element for ${tabId}`);
      const favicon = tabIcon.querySelector('.tab-icon-favicon') as HTMLImageElement;
      if (favicon) {
        console.log(`[FAVICON-UPDATE] Found favicon element in tab icon, updating src from ${favicon.src} to ${faviconUrl}`);

        // Add visual feedback for favicon change
        favicon.style.transition = 'opacity 0.3s ease';
        favicon.style.opacity = '0.5';

        favicon.src = faviconUrl;

        // Restore opacity after image loads
        favicon.onload = () => {
          favicon.style.opacity = '1';
          console.log(`[FAVICON-UPDATE] Tab icon favicon loaded successfully for ${tabId}`);
        };

        favicon.onerror = () => {
          favicon.style.opacity = '1';
          console.warn(`[FAVICON-UPDATE] Failed to load tab icon favicon for ${tabId}: ${faviconUrl}`);
        };
      } else {
        console.warn(`[FAVICON-UPDATE] No favicon element found in tab icon for ${tabId}`);
      }
    } else {
      console.warn(`[FAVICON-UPDATE] No tab icon element found for ${tabId}`);
    }
  }

  private updateTabElementFavicon(tabId: string, faviconUrl: string): void {
    console.log(`[FAVICON-UPDATE] Updating tab element favicon for ${tabId}: ${faviconUrl}`);
    const tabElement = this.tabStripContent.querySelector(`[data-tab-id="${tabId}"]`) as HTMLElement;
    if (tabElement) {
      console.log(`[FAVICON-UPDATE] Found tab element for ${tabId}`);
      const favicon = tabElement.querySelector('.tab-favicon') as HTMLImageElement;
      if (favicon) {
        console.log(`[FAVICON-UPDATE] Found favicon element in tab element, updating src from ${favicon.src} to ${faviconUrl}`);

        // Add visual feedback for favicon change
        favicon.style.transition = 'opacity 0.3s ease';
        favicon.style.opacity = '0.5';

        favicon.src = faviconUrl;

        // Restore opacity after image loads
        favicon.onload = () => {
          favicon.style.opacity = '1';
          console.log(`[FAVICON-UPDATE] Tab element favicon loaded successfully for ${tabId}`);
        };

        favicon.onerror = () => {
          favicon.style.opacity = '1';
          console.warn(`[FAVICON-UPDATE] Failed to load tab element favicon for ${tabId}: ${faviconUrl}`);
        };
      } else {
        console.warn(`[FAVICON-UPDATE] No favicon element found in tab element for ${tabId}`);
      }
    } else {
      console.warn(`[FAVICON-UPDATE] No tab element found for ${tabId}`);
    }
  }

  private updateTabTitle(tabId: string, title: string): void {
    // Update tab data
    const tab = this.tabs.find(t => t.id === tabId);
    if (tab) {
      tab.title = title;
      console.log(`Updated title for tab ${tabId}: ${title}`);

      // Update the title in both icon strip and flyout
      this.updateTabIconTitle(tabId, title);
      this.updateTabElementTitle(tabId, title);
    }
  }

  private updateTabIconTitle(tabId: string, title: string): void {
    const tabIcon = this.tabStripIcons.querySelector(`[data-tab-id="${tabId}"]`) as HTMLElement;
    if (tabIcon) {
      tabIcon.title = title; // Update tooltip
    }
  }

  private updateTabElementTitle(tabId: string, title: string): void {
    const tabElement = this.tabStripContent.querySelector(`[data-tab-id="${tabId}"]`) as HTMLElement;
    if (tabElement) {
      const titleElement = tabElement.querySelector('.tab-title') as HTMLElement;
      if (titleElement) {
        const displayTitle = title.length > 20 ? title.substring(0, 17) + '...' : title;
        titleElement.textContent = displayTitle;
        titleElement.title = title; // Full title on hover
      }
    }
  }

  private updateTabNavigation(tabId: string, url: string, isSuccess: boolean, isLoading: boolean): void {
    // Update tab data
    const tab = this.tabs.find(t => t.id === tabId);
    if (tab) {
      if (isSuccess) {
        tab.url = url;
      }
      tab.isLoading = isLoading;
      console.log(`Updated navigation for tab ${tabId}: ${url}, loading: ${isLoading}`);

      // Update loading indicators
      this.updateTabLoadingIndicators(tabId, isLoading);
    }
  }

  private updateTabLoadingState(tabId: string, isLoading: boolean): void {
    // Update tab data
    const tab = this.tabs.find(t => t.id === tabId);
    if (tab) {
      tab.isLoading = isLoading;
      console.log(`Updated loading state for tab ${tabId}: ${isLoading}`);

      // Update loading indicators
      this.updateTabLoadingIndicators(tabId, isLoading);
    }
  }

  private updateTabLoadingIndicators(tabId: string, isLoading: boolean): void {
    const tabElement = this.tabStripContent.querySelector(`[data-tab-id="${tabId}"]`) as HTMLElement;
    if (tabElement) {
      const loadingIndicator = tabElement.querySelector('.tab-loading') as HTMLElement;
      if (loadingIndicator) {
        loadingIndicator.style.display = isLoading ? 'flex' : 'none';
      }
    }
  }

  private createTabIcon(tab: TabInfo): HTMLElement {
    const iconElement = document.createElement('div');
    iconElement.className = `tab-icon ${tab.isActive ? 'active' : ''}`;
    iconElement.dataset['tabId'] = tab.id;
    iconElement.title = tab.title;

    // Favicon for the icon
    const favicon = document.createElement('img');
    favicon.className = 'tab-icon-favicon';

    // Create a better default favicon
    const defaultFavicon = 'data:image/svg+xml;base64,' + btoa(`
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 1a7 7 0 100 14A7 7 0 008 1zM7 6a1 1 0 112 0v2a1 1 0 11-2 0V6z"/>
        <circle cx="8" cy="11" r="1"/>
      </svg>
    `);

    // Better favicon handling with validation
    const faviconUrl = tab.favicon && typeof tab.favicon === 'string' && tab.favicon.trim() !== ''
      ? tab.favicon
      : defaultFavicon;

    favicon.src = faviconUrl;
    favicon.alt = tab.title || 'Tab';
    favicon.onerror = () => {
      console.warn(`Failed to load favicon for tab ${tab.id}: ${faviconUrl}`);
      favicon.src = defaultFavicon;
    };

    // Add loading event for debugging
    favicon.onload = () => {
      console.log(`Favicon loaded successfully for tab ${tab.id}: ${faviconUrl}`);
    };

    // Icon click handler with validation
    iconElement.addEventListener('click', () => {
      if (!tab.isActive && tab.id && typeof tab.id === 'string') {
        this.activateTab(tab.id);
      } else if (!tab.id) {
        console.error('Tab icon clicked but tab has no ID:', tab);
        this.showNotification('Tab has invalid ID', 'error');
      }
    });

    // Individual icon hover handlers for enhanced responsiveness
    iconElement.addEventListener('mouseenter', () => {
      this.onTabStripEnter();
    });

    iconElement.addEventListener('mouseleave', () => {
      this.onTabStripLeave();
    });

    iconElement.appendChild(favicon);
    return iconElement;
  }

  private createTabElement(tab: TabInfo): HTMLElement {
    const tabElement = document.createElement('div');
    tabElement.className = `tab ${tab.isActive ? 'active' : ''}`;
    tabElement.dataset['tabId'] = tab.id;

    // Favicon with better fallback and error handling
    const favicon = document.createElement('img');
    favicon.className = 'tab-favicon';

    // Create a better default favicon
    const defaultFavicon = 'data:image/svg+xml;base64,' + btoa(`
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor">
        <path d="M8 1a7 7 0 100 14A7 7 0 008 1zM7 6a1 1 0 112 0v2a1 1 0 11-2 0V6z"/>
        <circle cx="8" cy="11" r="1"/>
      </svg>
    `);

    // Better favicon handling with validation
    const faviconUrl = tab.favicon && typeof tab.favicon === 'string' && tab.favicon.trim() !== ''
      ? tab.favicon
      : defaultFavicon;

    favicon.src = faviconUrl;
    favicon.alt = tab.title || 'Tab';
    favicon.onerror = () => {
      console.warn(`Failed to load favicon for tab ${tab.id}: ${faviconUrl}`);
      favicon.src = defaultFavicon;
    };

    // Title with better truncation
    const title = document.createElement('span');
    title.className = 'tab-title';
    const displayTitle = tab.title || 'New Tab';
    title.textContent = displayTitle.length > 20 ? displayTitle.substring(0, 17) + '...' : displayTitle;
    title.title = displayTitle; // Full title on hover

    // Loading indicator
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'tab-loading';
    loadingIndicator.innerHTML = '⟳';
    loadingIndicator.style.display = tab.isLoading ? 'flex' : 'none';

    // Close button with better styling
    const closeButton = document.createElement('button');
    closeButton.className = 'tab-close';
    closeButton.innerHTML = '×';
    closeButton.title = 'Close tab';
    closeButton.addEventListener('click', (e) => {
      e.stopPropagation();
      if (tab.id && typeof tab.id === 'string') {
        this.closeTab(tab.id);
      } else {
        console.error('Close button clicked but tab has no valid ID:', tab);
        this.showNotification('Cannot close tab: invalid ID', 'error');
      }
    });

    // Tab click handler with visual feedback and validation
    tabElement.addEventListener('click', () => {
      if (!tab.isActive && tab.id && typeof tab.id === 'string') {
        this.activateTab(tab.id);
      } else if (!tab.id) {
        console.error('Tab clicked but tab has no ID:', tab);
        this.showNotification('Tab has invalid ID', 'error');
      }
    });

    // Add hover effect for better UX (adjusted for vertical layout)
    tabElement.addEventListener('mouseenter', () => {
      if (!tab.isActive) {
        tabElement.style.transform = 'translateX(2px)';
      }
    });

    tabElement.addEventListener('mouseleave', () => {
      if (!tab.isActive) {
        tabElement.style.transform = '';
      }
    });

    tabElement.appendChild(favicon);
    tabElement.appendChild(title);
    if (tab.isLoading) {
      tabElement.appendChild(loadingIndicator);
    }
    tabElement.appendChild(closeButton);

    return tabElement;
  }

  private async createNewTab(): Promise<void> {
    console.log('[RENDERER] createNewTab called');

    // For automatic tab creation (first tab), use defaults
    if (this.tabs.length === 0) {
      console.log('[RENDERER] Creating first tab with defaults');
      await this.createNewTabWithData({ url: 'https://www.google.com', name: 'New Tab' });
      return;
    }

    // For user-initiated tab creation, show dialog
    console.log('[RENDERER] Opening new tab dialog for user input');
    this.showNewTabDialogAsync();
  }

  private async createTabWithRetry(request: any, maxRetries: number): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          await new Promise(resolve => setTimeout(resolve, 200 * attempt));
        }

        const newTab = await window.sideView.tabs.create(request);
        return newTab;

      } catch (error) {
        lastError = error as Error;
        console.warn(`Attempt ${attempt} failed: ${(error as Error).message}`);

        if (attempt === maxRetries) {
          break;
        }
      }
    }

    throw new Error(`Tab creation failed after ${maxRetries} attempts. Last error: ${lastError?.message}`);
  }



  private async showNewTabDialogAsync(): Promise<void> {
    // Notify main process that modal is opening (same as settings dialog)
    try {
      await window.sideView.panel.setModalState(true);
    } catch (error) {
      console.error('Failed to set modal state:', error);
      // Continue anyway - modal can still work without BrowserView detachment
    }

    const dialog = this.createNewTabDialog();
    document.body.appendChild(dialog);

    // Focus on the URL input
    const urlInput = dialog.querySelector('#tab-url-input') as HTMLInputElement;
    if (urlInput) {
      urlInput.focus();
      urlInput.select();
    }
  }

  private createNewTabDialog(): HTMLElement {
    const overlay = document.createElement('div');
    overlay.className = 'dialog-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2147483647; /* Maximum z-index to ensure dialog appears above BrowserView */
      pointer-events: auto;
      user-select: none;
    `;

    const dialog = document.createElement('div');
    dialog.className = 'new-tab-dialog';
    dialog.style.cssText = `
      background: var(--bg-primary, #1e1e1e);
      border: 1px solid var(--border-color, #333);
      border-radius: 8px;
      padding: 20px;
      min-width: 300px;
      max-width: 400px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      position: relative;
      z-index: 2147483647;
      pointer-events: auto;
      user-select: text;
    `;

    dialog.innerHTML = `
      <h3 style="margin: 0 0 16px 0; color: var(--text-primary, #fff);">Create New Tab</h3>

      <div style="margin-bottom: 12px;">
        <label for="tab-url-input" style="display: block; margin-bottom: 4px; color: var(--text-secondary, #ccc); font-size: 12px;">URL</label>
        <input type="url" id="tab-url-input" value="https://" placeholder="https://example.com" style="
          width: 100%;
          padding: 8px;
          border: 1px solid var(--border-color, #333);
          border-radius: 4px;
          background: var(--bg-secondary, #2d2d2d);
          color: var(--text-primary, #fff);
          font-size: 14px;
          box-sizing: border-box;
        ">
      </div>

      <div style="margin-bottom: 16px;">
        <label for="tab-name-input" style="display: block; margin-bottom: 4px; color: var(--text-secondary, #ccc); font-size: 12px;">Tab Name (optional)</label>
        <input type="text" id="tab-name-input" placeholder="New Tab" style="
          width: 100%;
          padding: 8px;
          border: 1px solid var(--border-color, #333);
          border-radius: 4px;
          background: var(--bg-secondary, #2d2d2d);
          color: var(--text-primary, #fff);
          font-size: 14px;
          box-sizing: border-box;
        ">
      </div>

      <div style="display: flex; gap: 8px; justify-content: flex-end;">
        <button id="cancel-tab-btn" style="
          padding: 8px 16px;
          border: 1px solid var(--border-color, #333);
          border-radius: 4px;
          background: var(--bg-secondary, #2d2d2d);
          color: var(--text-primary, #fff);
          cursor: pointer;
          font-size: 14px;
        ">Cancel</button>
        <button id="create-tab-btn" style="
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          background: var(--accent-color, #007acc);
          color: white;
          cursor: pointer;
          font-size: 14px;
        ">Create Tab</button>
      </div>
    `;

    overlay.appendChild(dialog);

    // Event handlers
    const urlInput = dialog.querySelector('#tab-url-input') as HTMLInputElement;
    const nameInput = dialog.querySelector('#tab-name-input') as HTMLInputElement;
    const cancelBtn = dialog.querySelector('#cancel-tab-btn') as HTMLButtonElement;
    const createBtn = dialog.querySelector('#create-tab-btn') as HTMLButtonElement;

    const closeDialog = async () => {
      try {
        await window.sideView.panel.setModalState(false);
      } catch (error) {
        console.error('Failed to clear modal state:', error);
        // Continue with DOM cleanup anyway
      }

      // Clean up dialog elements
      try {
        if (overlay.parentNode) {
          overlay.parentNode.removeChild(overlay);
        }
      } catch (cleanupError) {
        console.error('Failed to clean up dialog elements:', cleanupError);
      }
    };

    const createTab = async () => {
      let url = urlInput.value.trim();
      const name = nameInput.value.trim() || 'New Tab';

      if (!url) {
        this.showTabDialogError('URL is required');
        urlInput.focus();
        return;
      }

      // Validate and correct URL format
      try {
        new URL(url);
      } catch {
        // Try adding https:// if missing
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          url = 'https://' + url;
          try {
            new URL(url);
            urlInput.value = url;
          } catch {
            this.showTabDialogError('Invalid URL format. Please enter a valid URL (e.g., https://example.com)');
            urlInput.focus();
            return;
          }
        } else {
          this.showTabDialogError('Invalid URL format. Please enter a valid URL (e.g., https://example.com)');
          urlInput.focus();
          return;
        }
      }

      console.log(`[RENDERER] Creating tab: ${name} - ${url}`);
      await closeDialog();

      // Call the actual tab creation logic
      await this.createNewTabWithData({ url, name });
    };

    // Event listeners
    cancelBtn.addEventListener('click', closeDialog);
    createBtn.addEventListener('click', createTab);

    // Close on overlay click
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        closeDialog();
      }
    });

    // Handle Enter key
    const handleEnter = (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        createTab();
      } else if (e.key === 'Escape') {
        closeDialog();
      }
    };

    urlInput.addEventListener('keydown', handleEnter);
    nameInput.addEventListener('keydown', handleEnter);

    return overlay;
  }

  private showTabDialogError(message: string): void {
    // Find existing error message and remove it
    const existingError = document.querySelector('.tab-dialog-error-message');
    if (existingError) {
      existingError.remove();
    }

    // Create new error message
    const dialog = document.querySelector('.new-tab-dialog');
    if (dialog) {
      const errorDiv = document.createElement('div');
      errorDiv.className = 'tab-dialog-error-message';
      errorDiv.style.cssText = `
        color: #ff6b6b;
        font-size: 12px;
        margin-top: 8px;
        padding: 4px 8px;
        background: rgba(255, 107, 107, 0.1);
        border: 1px solid rgba(255, 107, 107, 0.3);
        border-radius: 4px;
      `;
      errorDiv.textContent = message;

      const buttonsDiv = dialog.querySelector('div:last-child');
      if (buttonsDiv) {
        dialog.insertBefore(errorDiv, buttonsDiv);
      }
    }
  }

  private async createNewTabWithData(data: { url: string; name: string }): Promise<void> {
    // This is the actual tab creation logic, extracted from createNewTab
    try {
      // Show loading notification
      this.showNotification('Creating new tab...', 'info');

      // TEMPORARY FIX: Use 'shared' session mode like working sample tabs
      // This avoids SSL/TLS issues with isolated sessions until certificate verification is fixed
      // Create tab with validated URL and better error handling
      const newTab = await this.createTabWithRetry({
        url: data.url,
        name: data.name,
        sessionMode: 'shared' as any, // FIXED: Use shared session like sample tabs to avoid SSL issues
        activate: true
      }, 3);

      // Ensure tab is in local state
      if (!this.tabs.find(tab => tab.id === newTab.id)) {
        this.tabs.push(newTab);
        this.renderTabs();
      }

      // Force immediate WebApp attachment with better error handling
      try {
        await this.forceWebAppAttachment(newTab.id);
      } catch (attachError) {
        console.warn('WebApp attachment failed, but tab was created:', attachError);
        // Don't fail the entire operation if attachment fails
      }

      this.showNotification('Tab created successfully', 'success');

    } catch (error) {
      console.error('[RENDERER] Failed to create new tab:', error);

      // Log detailed error information
      if (error instanceof Error) {
        console.error('[RENDERER] Error name:', error.name);
        console.error('[RENDERER] Error message:', error.message);
        console.error('[RENDERER] Error stack:', error.stack);
      } else {
        console.error('[RENDERER] Error (non-Error object):', JSON.stringify(error, null, 2));
      }

      // Enhanced error messages based on error type
      let errorMessage = 'Failed to create new tab';
      if (error instanceof Error) {
        if (error.message.includes('Maximum number of tabs')) {
          errorMessage = 'Maximum number of tabs reached';
        } else if (error.message.includes('WebEngineService')) {
          errorMessage = 'Web engine not available. Please try again.';
        } else if (error.message.includes('AppManagerService')) {
          errorMessage = 'App manager not available. Please try again.';
        } else if (error.message.includes('not initialized')) {
          errorMessage = 'Services not ready. Please wait and try again.';
        } else if (error.message.includes('Failed to create app')) {
          errorMessage = `Failed to create tab: ${error.message}`;
        } else {
          errorMessage = `Failed to create new tab: ${error.message}`;
        }
      }

      this.showNotification(errorMessage, 'error');
    }
  }

  // **CRITICAL FIX: Enhanced Tab Activation with Service Status Tracking**
  private async activateTab(tabId: string): Promise<void> {
    console.log(`[TAB-ACTIVATION] Starting activation: ${tabId}`);

    // Validate tabId parameter
    if (!tabId || typeof tabId !== 'string') {
      console.error(`[TAB-ACTIVATION] ERROR: Invalid tabId provided: ${tabId}`);
      this.showNotification('Invalid tab ID', 'error');
      return;
    }

    // Check if tab exists in local state
    const tab = this.tabs.find(t => t.id === tabId);
    if (!tab) {
      console.error(`[TAB-ACTIVATION] ERROR: Tab ${tabId} not found in local state`);
      this.showNotification('Tab not found', 'error');
      return;
    }

    // Validate tab object has required properties
    if (!tab.id || typeof tab.id !== 'string') {
      console.error(`[TAB-ACTIVATION] ERROR: Tab object has invalid ID: ${tab.id}`);
      this.showNotification('Tab has invalid ID', 'error');
      return;
    }

    // Prevent multiple simultaneous activations of the same tab
    if (tab.isActive) {
      console.log(`[TAB-ACTIVATION] Tab ${tabId} already active, skipping`);
      return;
    }

    try {
      // Step 1: Update local UI state
      console.log(`[TAB-ACTIVATION] Step 1: Updating local UI state for ${tabId}`);
      this.setActiveTab(tabId);

      // Step 2: Activate in main process with immediate attachment
      console.log(`[TAB-ACTIVATION] Step 2: Calling main process activation for ${tabId}`);
      await this.activateTabWithRetry(tabId, 3);

      // Step 3: Force immediate WebApp attachment (this is the key fix)
      console.log(`[TAB-ACTIVATION] Step 3: Forcing immediate WebApp attachment for ${tabId}`);
      await this.forceWebAppAttachment(tabId);

      console.log(`[TAB-ACTIVATION] SUCCESS: Tab ${tabId} activated with immediate content`);
    } catch (error) {
      console.error(`[TAB-ACTIVATION] ERROR: Failed to activate tab ${tabId}:`, error);
      this.showNotification(`Failed to activate tab: ${(error as Error).message}`, 'error');

      // Revert local state if main process activation failed
      const previouslyActiveTab = this.tabs.find(tab => tab.isActive && tab.id !== tabId);
      if (previouslyActiveTab) {
        console.log(`[TAB-ACTIVATION] Reverting to previous tab: ${previouslyActiveTab.id}`);
        this.setActiveTab(previouslyActiveTab.id);
      } else {
        this.tabs.forEach(t => t.isActive = false);
        this.activeTabId = null;
        this.renderTabs();
      }
    }
  }

  private async activateTabWithRetry(tabId: string, maxRetries: number): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          await new Promise(resolve => setTimeout(resolve, 100 * attempt));
        }

        await window.sideView.tabs.activate(tabId);
        return; // Success!

      } catch (error) {
        lastError = error as Error;
        console.warn(`Attempt ${attempt} failed: ${(error as Error).message}`);

        if (attempt === maxRetries) {
          break;
        }
      }
    }

    throw new Error(`Tab activation failed after ${maxRetries} attempts. Last error: ${lastError?.message}`);
  }

  private async forceWebAppAttachment(tabId: string): Promise<void> {
    try {
      // Force the panel to show and attach the WebApp immediately
      // This is what normally happens only on hover - we need it to happen on tab activation
      await window.sideView.panel.attachActiveWebApp();

    } catch (error) {
      console.error(`Failed to attach WebApp ${tabId}:`, error);
      // Don't throw here - tab activation should still work even if attachment fails
    }
  }

  private setActiveTab(tabId: string): void {
    this.activeTabId = tabId;

    // Update tab states immediately for responsive UI
    this.tabs.forEach(tab => {
      tab.isActive = tab.id === tabId;
    });

    // Update UI - both icons and flyout tabs efficiently
    this.updateTabActiveStates();
  }

  private updateTabActiveStates(): void {
    // Update tab icons
    const tabIcons = this.tabStripIcons.querySelectorAll('.tab-icon');
    tabIcons.forEach(icon => {
      const tabId = (icon as HTMLElement).dataset['tabId'];
      const tab = this.tabs.find(t => t.id === tabId);
      if (tab) {
        icon.classList.toggle('active', tab.isActive);
      }
    });

    // Update flyout tabs
    const tabElements = this.tabStripContent.querySelectorAll('.tab');
    tabElements.forEach(element => {
      const tabId = (element as HTMLElement).dataset['tabId'];
      const tab = this.tabs.find(t => t.id === tabId);
      if (tab) {
        element.classList.toggle('active', tab.isActive);
      }
    });
  }

  private async closeTab(tabId: string): Promise<void> {
    // Validate tabId parameter
    if (!tabId || typeof tabId !== 'string') {
      console.error('Invalid tabId provided to closeTab:', tabId);
      this.showNotification('Invalid tab ID', 'error');
      return;
    }

    try {
      await window.sideView.tabs.close(tabId);

      // Remove from local tabs array
      this.tabs = this.tabs.filter(tab => tab.id !== tabId);

      // If this was the active tab, clear active state
      if (this.activeTabId === tabId) {
        this.activeTabId = null;
        this.clearWebContent();
      }

      this.renderTabs();
    } catch (error) {
      console.error('Failed to close tab:', error);
      this.showNotification('Failed to close tab', 'error');
    }
  }

  // **OVERLAY APPROACH: Modal state management methods no longer needed**
  // Removed clearModalStateWithRetry and performEmergencyWidgetRestart methods
  // since we no longer manipulate BrowserView state for modals

  /**
   * Handle new tab created via IPC (e.g., from pop-ups)
   * This ensures pop-up tabs are properly integrated into the tab management system
   */
  private async handleNewTabCreated(tab: TabInfo): Promise<void> {
    console.log(`[TAB-CREATED] Processing new tab: ${tab.id} - ${tab.title}`);

    // Validate the tab object
    if (!tab.id || typeof tab.id !== 'string') {
      console.error('[TAB-CREATED] Invalid tab received:', tab);
      this.showNotification('Invalid tab created', 'error');
      return;
    }

    // Check if tab already exists in local state
    const existingTab = this.tabs.find(t => t.id === tab.id);
    if (existingTab) {
      console.log(`[TAB-CREATED] Tab ${tab.id} already exists, updating state`);
      // Update existing tab properties
      Object.assign(existingTab, tab);
    } else {
      console.log(`[TAB-CREATED] Adding new tab ${tab.id} to local state`);
      // Add new tab to local state
      this.tabs.push(tab);
    }

    // Re-render tabs to show the new tab in the UI
    this.renderTabs();

    // If the tab is marked as active, activate it immediately
    if (tab.isActive) {
      console.log(`[TAB-CREATED] Activating new tab ${tab.id}`);
      try {
        // Update local state to reflect the active tab
        this.setActiveTab(tab.id);

        // Force immediate WebApp attachment for pop-up tabs
        await this.forceWebAppAttachment(tab.id);

        console.log(`[TAB-CREATED] Successfully activated new tab ${tab.id}`);
      } catch (error) {
        console.error(`[TAB-CREATED] Failed to activate new tab ${tab.id}:`, error);
        this.showNotification(`Failed to activate new tab: ${(error as Error).message}`, 'error');
      }
    }

    // Show success notification for pop-up tabs
    this.showNotification(`New tab opened: ${tab.title}`, 'success');
  }
}

// Expose PanelRenderer globally for debugging
(window as any).PanelRenderer = PanelRenderer;

// Track renderer errors globally
(window as any).rendererErrors = [];

// Override console.error to track errors
const originalConsoleError = console.error;
console.error = (...args: any[]) => {
  (window as any).rendererErrors.push(args.join(' '));
  originalConsoleError.apply(console, args);
};

// **CRITICAL FIX 1: Eliminate Dual Initialization (Dead Code)**
// The dual initialization was causing race conditions and state conflicts
// Only use DOMContentLoaded to ensure proper initialization order

let rendererInitialized = false;

function initializeRenderer() {
  if (rendererInitialized) {
    return;
  }

  try {
    const renderer = new PanelRenderer();
    (window as any).panelRendererInstance = renderer;
    rendererInitialized = true;
    console.log('Panel renderer initialized successfully');
  } catch (error) {
    console.error('Failed to initialize PanelRenderer:', error);
    (window as any).rendererErrors.push(`Init error: ${error}`);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeRenderer);
} else {
  initializeRenderer();
}
