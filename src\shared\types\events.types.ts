/**
 * Event system types and interfaces
 * Migrated from C# SideView.Core.Events
 */

export interface IAppEvent {
  readonly eventType: string;
  readonly timestamp: Date;
  readonly source?: string;
}

export abstract class AppEvent implements IAppEvent {
  public readonly eventType: string;
  public readonly timestamp: Date;
  public readonly source?: string;

  constructor(eventType: string, source?: string) {
    this.eventType = eventType;
    this.timestamp = new Date();
    if (source !== undefined) {
      this.source = source;
    }
  }
}

// Core module events
export class CoreModuleStartedEvent extends AppEvent {
  constructor() {
    super('CoreModuleStarted', 'Core');
  }
}

export class CoreModuleStoppedEvent extends AppEvent {
  constructor() {
    super('CoreModuleStopped', 'Core');
  }
}

// UI module events
export class UIModuleStartedEvent extends AppEvent {
  constructor() {
    super('UIModuleStarted', 'UI');
  }
}

export class UIModuleStoppedEvent extends AppEvent {
  constructor() {
    super('UIModuleStopped', 'UI');
  }
}

export class PanelShownEvent extends AppEvent {
  constructor() {
    super('PanelShown', 'UI');
  }
}

export class PanelHiddenEvent extends AppEvent {
  constructor() {
    super('PanelHidden', 'UI');
  }
}

export class PanelVisibilityChangedEventArgs {
  constructor(
    public readonly isVisible: boolean,
    public readonly animated: boolean
  ) {}
}

// WebEngine module events
export class WebEngineModuleStartedEvent extends AppEvent {
  constructor() {
    super('WebEngineModuleStarted', 'WebEngine');
  }
}

export class WebAppActivatedEvent extends AppEvent {
  constructor(public readonly appId: string) {
    super('WebAppActivated', 'WebEngine');
  }
}

export class WebAppNavigationCompletedEvent extends AppEvent {
  constructor(
    public readonly appId: string,
    public readonly url: string,
    public readonly isSuccess: boolean
  ) {
    super('WebAppNavigationCompleted', 'WebEngine');
  }
}

// Navigation event args
export class NavigationEventArgs {
  constructor(
    public readonly url: string,
    public readonly isMainFrame: boolean = true
  ) {}
}

// Title changed event args
export class TitleChangedEventArgs {
  constructor(
    public readonly title: string,
    public readonly appId: string
  ) {}
}

// AppManager module events
export class AppManagerModuleStartedEvent extends AppEvent {
  constructor() {
    super('AppManagerModuleStarted', 'AppManager');
  }
}

export class AppCreatedEvent extends AppEvent {
  constructor(public readonly app: any) {
    super('AppCreated', 'AppManager');
  }
}

export class AppUpdatedEvent extends AppEvent {
  constructor(public readonly app: any) {
    super('AppUpdated', 'AppManager');
  }
}

export class AppDeletedEvent extends AppEvent {
  constructor(
    public readonly appId: string,
    public readonly appName: string
  ) {
    super('AppDeleted', 'AppManager');
  }
}

export class AppIconUpdatedEvent extends AppEvent {
  constructor(
    public readonly appId: string,
    public readonly iconPath: string
  ) {
    super('AppIconUpdated', 'AppManager');
  }
}

// Activation events
export class ActivationEventArgs {
  constructor(
    public readonly source: ActivationSource,
    public readonly data?: any
  ) {}
}

export enum ActivationSource {
  MouseHover = 'mouse-hover',
  MouseClick = 'mouse-click',
  Hotkey = 'hotkey',
  Manual = 'manual',
  Focus = 'focus'
}

// Notification events
export class NotificationReceivedEvent extends AppEvent {
  constructor(
    public readonly appId: string,
    public readonly title: string,
    public readonly body: string,
    public readonly icon?: string
  ) {
    super('NotificationReceived', 'Notifications');
  }
}

// Hotkey events
export class HotkeyPressedEvent extends AppEvent {
  constructor(
    public readonly hotkey: string,
    public readonly action: string
  ) {
    super('HotkeyPressed', 'Hotkeys');
  }
}

// Update events
export class UpdateAvailableEvent extends AppEvent {
  constructor(
    public readonly version: string,
    public readonly releaseNotes: string
  ) {
    super('UpdateAvailable', 'Updater');
  }
}

export class UpdateDownloadedEvent extends AppEvent {
  constructor(public readonly version: string) {
    super('UpdateDownloaded', 'Updater');
  }
}

// Tab and WebApp events
export class WebAppFaviconUpdatedEvent extends AppEvent {
  public readonly appId: string;
  public readonly faviconUrl: string;

  constructor(appId: string, faviconUrl: string) {
    super('WebAppFaviconUpdated', 'WebEngine');
    this.appId = appId;
    this.faviconUrl = faviconUrl;
  }
}

export class WebAppNewWindowRequestedEvent extends AppEvent {
  public readonly appId: string;
  public readonly url: string;
  public readonly frameName: string | undefined;

  constructor(appId: string, url: string, frameName?: string) {
    super('WebAppNewWindowRequested', 'WebEngine');
    this.appId = appId;
    this.url = url;
    this.frameName = frameName;
  }
}

export class WebAppTitleChangedEvent extends AppEvent {
  public readonly appId: string;
  public readonly title: string;

  constructor(appId: string, title: string) {
    super('WebAppTitleChanged', 'WebEngine');
    this.appId = appId;
    this.title = title;
  }
}

export class WebAppLoadingStateChangedEvent extends AppEvent {
  public readonly appId: string;
  public readonly isLoading: boolean;

  constructor(appId: string, isLoading: boolean) {
    super('WebAppLoadingStateChanged', 'WebEngine');
    this.appId = appId;
    this.isLoading = isLoading;
  }
}

export class TabFaviconUpdatedEvent extends AppEvent {
  public readonly tabId: string;
  public readonly faviconUrl: string;

  constructor(tabId: string, faviconUrl: string) {
    super('TabFaviconUpdated', 'TabManager');
    this.tabId = tabId;
    this.faviconUrl = faviconUrl;
  }
}

// Event handler types
export type EventHandler<T extends IAppEvent> = (event: T) => void | Promise<void>;

export interface IAppEventBus {
  publish<T extends IAppEvent>(event: T): Promise<void>;
  subscribe<T extends IAppEvent>(eventType: string, handler: EventHandler<T>): void;
  unsubscribe<T extends IAppEvent>(eventType: string, handler: EventHandler<T>): void;
  clear(): void;
}
